using UnityEngine;

[ExecuteInEditMode] // Para funcionar também no editor
public class RectMask3DController : MonoBehaviour
{
    [Tooltip("O RectTransform que definirá a área da máscara.")]
    public RectTransform maskingRect;

    [Tooltip("O Renderer do objeto 3D que será mascarado. Deve usar um material com o shader RectMask3D.")]
    public Renderer targetRenderer;

    [<PERSON>lt<PERSON>("A distância de suavização (fade) nas bordas da máscara.")]
    [Range(0, 1)]
    public float fadeDistance = 0f;

    private MaterialPropertyBlock _propBlock;
    private Vector3[] _corners = new Vector3[4];
    private static readonly int MaskMinID = Shader.PropertyToID("_MaskMin");
    private static readonly int MaskMaxID = Shader.PropertyToID("_MaskMax");
    private static readonly int FadeDistanceID = Shader.PropertyToID("_FadeDistance");

    void LateUpdate()
    {
        if (maskingRect == null || targetRenderer == null)
        {
            return;
        }

        if (_propBlock == null)
        {
            _propBlock = new MaterialPropertyBlock();
        }

        // Pega os 4 cantos do RectTransform no espaço do mundo
        maskingRect.GetWorldCorners(_corners);

        // _corners[0] = bottom-left
        // _corners[1] = top-left
        // _corners[2] = top-right
        // _corners[3] = bottom-right

        // Calcula os bounds reais considerando rotação
        // Quando o RectTransform está rotacionado, precisamos encontrar o AABB (Axis-Aligned Bounding Box)
        // dos 4 cantos para que a máscara funcione corretamente
        Vector3 minBounds = _corners[0];
        Vector3 maxBounds = _corners[0];

        // Itera pelos 4 cantos para encontrar os valores mínimos e máximos em cada eixo
        for (int i = 1; i < 4; i++)
        {
            if (_corners[i].x < minBounds.x) minBounds.x = _corners[i].x;
            if (_corners[i].y < minBounds.y) minBounds.y = _corners[i].y;
            if (_corners[i].z < minBounds.z) minBounds.z = _corners[i].z;

            if (_corners[i].x > maxBounds.x) maxBounds.x = _corners[i].x;
            if (_corners[i].y > maxBounds.y) maxBounds.y = _corners[i].y;
            if (_corners[i].z > maxBounds.z) maxBounds.z = _corners[i].z;
        }

        // Atualiza as propriedades do material via MaterialPropertyBlock (mais otimizado)
        targetRenderer.GetPropertyBlock(_propBlock);
        _propBlock.SetVector(MaskMinID, new Vector4(minBounds.x, minBounds.y, minBounds.z, 0));
        _propBlock.SetVector(MaskMaxID, new Vector4(maxBounds.x, maxBounds.y, maxBounds.z, 0));
        _propBlock.SetFloat(FadeDistanceID, fadeDistance);
        targetRenderer.SetPropertyBlock(_propBlock);
    }

    // Método para facilitar a configuração a partir de outros scripts
    public void SetTarget(Renderer renderer)
    {
        Debug.Log("SetTarget called");
        targetRenderer = renderer;
        // Cria uma instância do material para este objeto para não afetar outros
        // que usam o mesmo material base.
        if (targetRenderer != null)
        {
            targetRenderer.material = new Material(targetRenderer.material);
        }
    }
    
    [ContextMenu("Set Target")]
    public void SetTarget()
    {
        if (targetRenderer != null)
        {
            targetRenderer.material = new Material(targetRenderer.material);
        }
    }
}