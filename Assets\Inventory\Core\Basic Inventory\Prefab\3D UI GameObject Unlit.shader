Shader "Custom/RectMask3D_BaseMaterial"
{
    Properties
    {
        _Color ("Color", Color) = (1,1,1,1)
        _MainTex ("Texture", 2D) = "white" {}
        _MaskMin ("Mask Min (World)", Vector) = (0,0,0,0)
        _MaskMax ("Mask Max (World)", Vector) = (0,0,0,0)
        _FadeDistance ("Fade Distance", Float) = 0.1
    }
    SubShader
    {
        Tags { "Queue"="Transparent" "RenderType"="Transparent" }
        LOD 100
        Blend SrcAlpha OneMinusSrcAlpha
        Cull Off
        ZWrite On

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
                float3 worldPos : TEXCOORD1;
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            fixed4 _Color;
            float4 _MaskMin;
            float4 _MaskMax;
            float _FadeDistance;

            v2f vert (appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);
                o.worldPos = mul(unity_ObjectToWorld, v.vertex).xyz;
                return o;
            }

            fixed4 frag (v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv) * _Color;

                // Calcula o alfa para cada borda com suavização
                float alphaX = smoothstep(_MaskMin.x, _MaskMin.x + _FadeDistance, i.worldPos.x) * (1.0 - smoothstep(_MaskMax.x - _FadeDistance, _MaskMax.x, i.worldPos.x));
                float alphaY = smoothstep(_MaskMin.y, _MaskMin.y + _FadeDistance, i.worldPos.y) * (1.0 - smoothstep(_MaskMax.y - _FadeDistance, _MaskMax.y, i.worldPos.y));

                // O alfa final é a multiplicação dos dois eixos
                float finalAlpha = alphaX * alphaY;

                // Aplica o alfa da máscara
                col.a *= finalAlpha;
                
                // Discarda pixels completamente transparentes para otimização
                clip(col.a - 0.001);

                return col;
            }
            ENDCG
        }
    }
}